#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步批量时尚换装工具
Async Batch Fashion Try-On Tool

使用方法:
python async_batch_fashion_tryon.py --input_folder path/to/photos --clothes_image path/to/clothes.png

支持异步并发处理，提高处理效率
"""

import asyncio
import aiohttp
import aiofiles
import time
import os
import argparse
import json
from pathlib import Path
from PIL import Image
from datetime import datetime
from config import *
import concurrent.futures
import threading

# 全局锁用于线程安全的PIL操作
pil_lock = threading.Lock()

def create_output_dirs():
    """创建输出目录"""
    for dir_name in OUTPUT_DIRS.values():
        os.makedirs(dir_name, exist_ok=True)

def get_image_files(folder_path):
    """获取文件夹中的所有图片文件"""
    image_files = []
    folder = Path(folder_path)
    
    if not folder.exists():
        print(f"❌ 文件夹不存在: {folder_path}")
        return []
    
    for ext in SUPPORTED_FORMATS:
        image_files.extend(folder.glob(f"*{ext}"))
        image_files.extend(folder.glob(f"*{ext.upper()}"))
    
    return [str(f) for f in image_files]

class AsyncFashionProcessor:
    """异步时尚换装处理器"""
    
    def __init__(self, max_concurrent=3):
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.session = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
        timeout = aiohttp.ClientTimeout(total=TIMEOUT_CONFIG["request_timeout"])
        self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()

    async def process_single_photo_async(self, model_image, clothes_image, photo_name):
        """异步处理单张照片的完整流程"""
        async with self.semaphore:  # 限制并发数
            print(f"\n🎯 [异步] 开始处理: {photo_name}")
            
            start_time = time.time()
            result = {
                "photo_name": photo_name,
                "model_image": model_image,
                "start_time": datetime.now().isoformat(),
                "steps": {},
                "success": False,
                "final_result": None,
                "processing_time": 0,
                "cost_ptc": 0
            }
            
            try:
                # 步骤1: 异步换装
                step1_result = await self.step1_fashion_tryon_async(model_image, clothes_image, photo_name)
                result["steps"]["step1_fashion"] = {
                    "success": step1_result is not None,
                    "result_path": step1_result,
                    "cost_ptc": API_COSTS["fashion_tryon"] if step1_result else 0
                }
                
                if not step1_result:
                    print(f"❌ [{photo_name}] 步骤1失败，跳过")
                    result["processing_time"] = time.time() - start_time
                    return result
                
                # 步骤2: 异步移除背景
                step2_result = await self.step2_remove_background_async(step1_result, photo_name)
                result["steps"]["step2_remove_bg"] = {
                    "success": step2_result is not None,
                    "result_path": step2_result,
                    "cost_ptc": API_COSTS["remove_background"] if step2_result else 0
                }
                
                if not step2_result:
                    print(f"❌ [{photo_name}] 步骤2失败，跳过")
                    result["processing_time"] = time.time() - start_time
                    return result
                
                # 步骤3: 同步添加白底背景 (PIL操作，使用线程池)
                step3_result = await self.step3_add_white_background_async(step2_result, photo_name)
                result["steps"]["step3_white_bg"] = {
                    "success": step3_result is not None,
                    "result_path": step3_result,
                    "cost_ptc": API_COSTS["white_background"]
                }
                
                if step3_result:
                    result["success"] = True
                    result["final_result"] = step3_result
                    print(f"🎉 [{photo_name}] 完整流程成功！")
                else:
                    print(f"❌ [{photo_name}] 步骤3失败")
                
                # 计算总成本和时间
                result["cost_ptc"] = sum(step["cost_ptc"] for step in result["steps"].values())
                result["processing_time"] = time.time() - start_time
                result["end_time"] = datetime.now().isoformat()
                
                return result
                
            except Exception as e:
                print(f"❌ [{photo_name}] 处理异常: {e}")
                result["processing_time"] = time.time() - start_time
                result["error"] = str(e)
                return result

    async def step1_fashion_tryon_async(self, model_image, clothes_image, photo_name):
        """异步步骤1: 302.AI-ComfyUI 换装"""
        print(f"🎯 [{photo_name}] 异步步骤1: 302.AI-ComfyUI 换装")
        
        if not os.path.exists(model_image):
            print(f"❌ 模特图片不存在: {model_image}")
            return None
        
        if not os.path.exists(clothes_image):
            print(f"❌ 服装图片不存在: {clothes_image}")
            return None
        
        url = f"{BASE_URL}/302/comfyui/clothes-changer/create-task"
        headers = {"Authorization": f"Bearer {API_KEY}"}
        
        try:
            # 异步读取文件
            async with aiofiles.open(model_image, 'rb') as model_file, \
                       aiofiles.open(clothes_image, 'rb') as clothes_file:
                
                model_content = await model_file.read()
                clothes_content = await clothes_file.read()
                
                data = aiohttp.FormData()
                data.add_field('modelImageFile', model_content, 
                              filename=os.path.basename(model_image), 
                              content_type='image/jpeg')
                data.add_field('clothesImageFile', clothes_content,
                              filename=os.path.basename(clothes_image),
                              content_type='image/png')
                data.add_field('modelImgSegLabels', FASHION_TRYON_CONFIG["modelImgSegLabels"])
                data.add_field('clothesImgSegLabels', FASHION_TRYON_CONFIG["clothesImgSegLabels"])
                
                print(f"📤 [{photo_name}] 发送异步换装任务请求...")
                async with self.session.post(url, headers=headers, data=data) as response:
                    print(f"📊 [{photo_name}] 响应状态码: {response.status}")
                    
                    if response.status in [200, 201]:
                        result = await response.json()
                        if result.get('code') == 200 and 'data' in result:
                            task_id = result['data']['taskId']
                            print(f"✅ [{photo_name}] 换装任务创建成功！任务ID: {task_id}")
                            return await self.wait_for_fashion_task_async(task_id, photo_name)
                        else:
                            print(f"❌ [{photo_name}] 任务创建失败: {result}")
                            return None
                    else:
                        error_text = await response.text()
                        print(f"❌ [{photo_name}] API请求失败: {response.status}")
                        print(f"📄 [{photo_name}] 响应内容: {error_text}")
                        return None
                        
        except Exception as e:
            print(f"❌ [{photo_name}] 请求失败: {e}")
            return None

    async def wait_for_fashion_task_async(self, task_id, photo_name):
        """异步等待换装任务完成"""
        print(f"⏳ [{photo_name}] 异步等待换装任务完成...")
        
        url = f"{BASE_URL}/302/comfyui/clothes-changer/check-task-status"
        headers = {"Authorization": f"Bearer {API_KEY}"}
        
        max_attempts = TIMEOUT_CONFIG["max_retry_attempts"]
        attempt = 0
        
        while attempt < max_attempts:
            attempt += 1
            print(f"🔄 [{photo_name}] 第{attempt}次异步查询...")
            
            try:
                params = {"taskId": task_id}
                async with self.session.get(url, headers=headers, params=params) as response:
                    if response.status == 200:
                        result = await response.json()
                        status = result.get('data', 'UNKNOWN')
                        
                        print(f"📈 [{photo_name}] 任务状态: {status}")
                        
                        if status == 'SUCCESS' and 'output' in result:
                            output = result['output']
                            result_url = output.get('resultUrl', '')
                            
                            print(f"🎉 [{photo_name}] 换装任务完成！")
                            print(f"🔗 [{photo_name}] 结果图URL: {result_url}")
                            
                            return await self.download_image_async(result_url, f"{photo_name}_step1_fashion.png")
                            
                        elif status in ['RUNNING', 'QUEUED', 'SUBMITTING']:
                            print(f"⏳ [{photo_name}] 任务进行中... 等待{TIMEOUT_CONFIG['task_check_interval']}秒后再次查询...")
                            await asyncio.sleep(TIMEOUT_CONFIG["task_check_interval"])
                            
                        else:
                            print(f"❌ [{photo_name}] 任务失败，状态: {status}")
                            return None
                    else:
                        print(f"❌ [{photo_name}] 查询失败: {response.status}")
                        await asyncio.sleep(TIMEOUT_CONFIG["task_check_interval"])
                        continue
                        
            except Exception as e:
                print(f"❌ [{photo_name}] 网络请求失败: {e}")
                await asyncio.sleep(TIMEOUT_CONFIG["task_check_interval"])
                continue
        
        print(f"⏰ [{photo_name}] 等待超时")
        return None

    async def step2_remove_background_async(self, image_path, photo_name):
        """异步步骤2: Clipdrop Remove-background 移除背景"""
        print(f"🎯 [{photo_name}] 异步步骤2: Clipdrop Remove-background 移除背景")
        
        if not image_path or not os.path.exists(image_path):
            print(f"❌ [{photo_name}] 图片文件不存在: {image_path}")
            return None
        
        url = f"{BASE_URL}/clipdrop/remove-background/v1"
        headers = {"x-api-key": API_KEY}
        
        try:
            async with aiofiles.open(image_path, 'rb') as image_file:
                image_content = await image_file.read()
                
                data = aiohttp.FormData()
                data.add_field('image_file', image_content,
                              filename=os.path.basename(image_path),
                              content_type='image/png')
                
                print(f"📤 [{photo_name}] 发送异步背景移除请求...")
                async with self.session.post(url, headers=headers, data=data) as response:
                    print(f"📊 [{photo_name}] 响应状态码: {response.status}")
                    
                    if response.status == 200:
                        output_path = os.path.join(OUTPUT_DIRS["temp"], f"{photo_name}_step2_no_background.png")
                        
                        # 异步写入文件
                        content = await response.read()
                        async with aiofiles.open(output_path, 'wb') as f:
                            await f.write(content)
                        
                        print(f"✅ [{photo_name}] 背景移除完成！已保存: {output_path}")
                        return output_path
                    else:
                        error_text = await response.text()
                        print(f"❌ [{photo_name}] 背景移除失败: {response.status}")
                        print(f"📄 [{photo_name}] 响应内容: {error_text}")
                        return None
                        
        except Exception as e:
            print(f"❌ [{photo_name}] 请求失败: {e}")
            return None

    async def step3_add_white_background_async(self, subject_image_path, photo_name):
        """异步步骤3: 本地PIL添加白底背景"""
        print(f"🎯 [{photo_name}] 异步步骤3: 本地PIL添加白底背景")

        if not subject_image_path or not os.path.exists(subject_image_path):
            print(f"❌ [{photo_name}] 主体图片不存在: {subject_image_path}")
            return None

        # 使用线程池执行PIL操作，避免阻塞事件循环
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            try:
                result = await loop.run_in_executor(
                    executor,
                    self._process_white_background_sync,
                    subject_image_path,
                    photo_name
                )
                return result
            except Exception as e:
                print(f"❌ [{photo_name}] 图片处理错误: {e}")
                return None

    def _process_white_background_sync(self, subject_image_path, photo_name):
        """同步处理白底背景（在线程池中执行）"""
        with pil_lock:  # 确保PIL操作的线程安全
            try:
                # 打开主体图片（已移除背景的PNG）
                subject = Image.open(subject_image_path).convert("RGBA")
                width, height = subject.size
                print(f"📏 [{photo_name}] 图片尺寸: {width}x{height}")

                # 创建白色背景
                background = Image.new('RGB', (width, height), WHITE_BACKGROUND_CONFIG["background_color"])

                # 将主体图片合成到白色背景上
                background.paste(subject, (0, 0), subject)

                # 保存结果
                output_path = os.path.join(OUTPUT_DIRS["batch_results"], f"{photo_name}_final_white_background.png")
                background.save(output_path, WHITE_BACKGROUND_CONFIG["output_format"],
                               dpi=(IMAGE_QUALITY["dpi"], IMAGE_QUALITY["dpi"]))

                print(f"✅ [{photo_name}] 白底背景合成完成！已保存: {output_path}")
                return output_path

            except Exception as e:
                print(f"❌ [{photo_name}] PIL处理错误: {e}")
                return None

    async def download_image_async(self, url, filename):
        """异步下载图片"""
        if not url:
            print("❌ URL为空，无法下载")
            return None

        try:
            print(f"📥 异步下载图片: {filename}")
            async with self.session.get(url) as response:
                if response.status == 200:
                    filepath = os.path.join(OUTPUT_DIRS["temp"], filename)

                    content = await response.read()
                    async with aiofiles.open(filepath, 'wb') as f:
                        await f.write(content)

                    print(f"✅ 图片已保存: {filepath}")
                    return filepath
                else:
                    print(f"❌ 下载失败: {response.status}")
                    return None
        except Exception as e:
            print(f"❌ 下载错误: {e}")
            return None

async def async_batch_process(image_files, clothes_image, max_concurrent=3):
    """异步批量处理主函数"""
    print(f"🚀 开始异步批量处理，最大并发数: {max_concurrent}")

    batch_start_time = time.time()
    batch_results = {
        "start_time": datetime.now().isoformat(),
        "total_photos": len(image_files),
        "results": [],
        "summary": {},
        "concurrent_level": max_concurrent
    }

    async with AsyncFashionProcessor(max_concurrent=max_concurrent) as processor:
        # 创建所有任务
        tasks = []
        for photo_path in image_files:
            photo_name = Path(photo_path).stem
            task = processor.process_single_photo_async(photo_path, clothes_image, photo_name)
            tasks.append(task)

        # 使用进度追踪执行所有任务
        results = []
        completed = 0

        # 使用asyncio.as_completed来获取完成的任务
        for coro in asyncio.as_completed(tasks):
            try:
                result = await coro
                results.append(result)
                completed += 1

                # 显示进度
                success_count = sum(1 for r in results if r.get("success", False))
                print(f"📈 进度: {completed}/{len(image_files)} 完成，成功: {success_count} ({success_count/completed*100:.1f}%)")

            except Exception as e:
                print(f"❌ 任务执行异常: {e}")
                # 创建失败结果
                failed_result = {
                    "success": False,
                    "error": str(e),
                    "processing_time": 0,
                    "cost_ptc": 0
                }
                results.append(failed_result)
                completed += 1

    # 处理结果
    batch_results["results"] = results

    # 生成批量测试总结
    batch_end_time = time.time()
    batch_results["end_time"] = datetime.now().isoformat()
    batch_results["total_time"] = batch_end_time - batch_start_time

    # 计算统计数据
    successful_results = [r for r in results if r.get("success", False)]
    total_cost = sum(r.get("cost_ptc", 0) for r in results)
    avg_time = sum(r.get("processing_time", 0) for r in results) / len(results) if results else 0

    batch_results["summary"] = {
        "total_photos": len(image_files),
        "successful_photos": len(successful_results),
        "success_rate": len(successful_results) / len(image_files) * 100 if image_files else 0,
        "total_cost_ptc": total_cost,
        "total_cost_cny": total_cost * PTC_TO_CNY,
        "average_time_per_photo": avg_time,
        "total_processing_time": batch_results["total_time"],
        "concurrent_level": max_concurrent
    }

    return batch_results

async def main_async():
    """异步主函数"""
    parser = argparse.ArgumentParser(description='异步批量时尚换装工具')
    parser.add_argument('--input_folder', type=str, help='输入图片文件夹路径')
    parser.add_argument('--clothes_image', type=str, help='服装图片路径')
    parser.add_argument('--max_concurrent', type=int, default=3, help='最大并发数 (默认3)')

    args = parser.parse_args()

    # 验证配置
    try:
        validate_config()
    except ValueError as e:
        print(f"❌ 配置错误: {e}")
        return

    # 创建输出目录
    create_output_dirs()

    # 获取输入路径
    if args.input_folder and args.clothes_image:
        input_folder = args.input_folder
        clothes_image = args.clothes_image
        max_concurrent = args.max_concurrent
    else:
        print("请输入文件路径:")
        input_folder = input("图片文件夹路径: ").strip()
        clothes_image = input("服装图片路径: ").strip()
        max_concurrent_input = input("最大并发数 (默认3): ").strip()
        max_concurrent = int(max_concurrent_input) if max_concurrent_input else 3

    # 获取所有图片文件
    image_files = get_image_files(input_folder)
    if not image_files:
        print(f"❌ 在文件夹 {input_folder} 中未找到支持的图片文件")
        return

    print("=" * 100)
    print("🚀 异步批量时尚换装工具")
    print("=" * 100)
    print("📝 工作流程:")
    print("   1. 302.AI-ComfyUI 换装 (异步)")
    print("   2. Clipdrop Remove-background 移除背景 (异步)")
    print("   3. 本地PIL 添加白底背景 (线程池)")
    print()
    print(f"📊 找到图片: {len(image_files)} 张")
    print(f"👕 服装模板: {clothes_image}")
    print(f"🔄 最大并发数: {max_concurrent}")
    print(f"💰 预估总成本: {len(image_files) * get_total_cost_per_image()} PTC (约{len(image_files) * get_total_cost_cny_per_image():.1f}元)")
    print()

    # 执行异步批量处理
    batch_results = await async_batch_process(image_files, clothes_image, max_concurrent)

    # 保存批量测试报告
    report_path = os.path.join(OUTPUT_DIRS["analysis"], "async_batch_test_report.json")
    async with aiofiles.open(report_path, 'w', encoding='utf-8') as f:
        await f.write(json.dumps(batch_results, ensure_ascii=False, indent=2))

    # 显示最终结果
    print("\n" + "=" * 100)
    print("🎉 异步批量测试完成！")
    print("=" * 100)
    print(f"📊 总体统计:")
    print(f"   📸 测试照片: {batch_results['summary']['total_photos']} 张")
    print(f"   ✅ 成功处理: {batch_results['summary']['successful_photos']} 张")
    print(f"   🏆 成功率: {batch_results['summary']['success_rate']:.1f}%")
    print(f"   💰 总成本: {batch_results['summary']['total_cost_ptc']:.1f} PTC (约{batch_results['summary']['total_cost_cny']:.1f}元)")
    print(f"   ⏱️  平均处理时间: {batch_results['summary']['average_time_per_photo']:.1f}秒/张")
    print(f"   🕐 总处理时间: {batch_results['summary']['total_processing_time']:.1f}秒")
    print(f"   🔄 并发级别: {batch_results['summary']['concurrent_level']}")
    print()
    print(f"📁 异步批量测试报告: {report_path}")
    print(f"📁 结果文件夹: {OUTPUT_DIRS['batch_results']}/")
    print("=" * 100)

def main():
    """同步主函数入口"""
    # 检查是否安装了异步依赖
    try:
        import aiohttp
        import aiofiles
    except ImportError as e:
        print("❌ 缺少异步依赖，请安装:")
        print("pip install aiohttp aiofiles")
        print(f"错误详情: {e}")
        return

    # 运行异步主函数
    try:
        asyncio.run(main_async())
    except KeyboardInterrupt:
        print("\n⚠️  用户中断了处理过程")
    except Exception as e:
        print(f"\n❌ 异步处理过程发生错误: {e}")

if __name__ == "__main__":
    main()
