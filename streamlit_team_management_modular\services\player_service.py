#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
球员服务
Player Service

提供球员相关的业务逻辑处理
"""

from typing import List, Optional, Tuple
import streamlit as st
import logging

from models.player import Player
from data.player_repository import PlayerRepository
from data.file_manager import FileManager
from utils.validation import DataValidator
from utils.image_utils import ImageProcessor
from utils.safe_file_manager import safe_file_manager
# 缓存已移除以解决数据一致性问题
from config.constants import UIConstants
from services.export_service import ExportService

# 配置日志
logger = logging.getLogger(__name__)


class PlayerService:
    """球员服务"""

    def __init__(self):
        # 获取当前用户ID
        user_id = self._get_current_user_id()
        self.player_repo = PlayerRepository(user_id)
        self.file_manager = FileManager(user_id)
        self.export_service = ExportService(user_id)

    def _get_current_user_id(self) -> str:
        """获取当前用户ID"""
        return st.session_state.get('user_id', '')
    
    def add_player(self, team_name: str, name: str, jersey_number: str, 
                   photo_file=None) -> Tuple[bool, str]:
        """
        添加新球员
        
        Args:
            team_name: 球队名称
            name: 球员姓名
            jersey_number: 球衣号码
            photo_file: 照片文件
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        # 验证球员姓名
        is_valid, error_msg = DataValidator.validate_player_name(name)
        if not is_valid:
            return False, error_msg
        
        # 验证球衣号码
        used_numbers = self.player_repo.get_used_jersey_numbers(team_name)
        is_valid, error_msg = DataValidator.validate_jersey_number(
            jersey_number, used_numbers
        )
        if not is_valid:
            return False, error_msg
        
        # 验证照片文件
        is_valid, error_msg = DataValidator.validate_image_file(photo_file)
        if not is_valid:
            return False, error_msg
        
        # 处理照片上传
        photo_filename = None
        if photo_file is not None:
            # 生成唯一文件名
            photo_filename = ImageProcessor.generate_unique_filename(photo_file.name)
            
            # 保存照片
            saved_path = self.file_manager.save_uploaded_photo(
                team_name, photo_file, photo_filename
            )
            if not saved_path:
                return False, "照片保存失败"
        
        # 创建球员对象
        player = Player.create_new(name, jersey_number, photo_filename)
        
        # 保存球员
        if self.player_repo.add_player(team_name, player):
            # 自动导出到AI处理文件夹
            self.export_service.auto_export_for_ai(team_name)

            # 标记球队状态已变化，并记录操作详情用于AI感知
            self._mark_team_stats_changed_with_action("add_player", {
                "name": name,
                "jersey_number": jersey_number,
                "has_photo": photo_filename is not None
            })

            success_msg = UIConstants.STATUS_MESSAGES["success_add"].format(
                name=name, number=jersey_number
            )
            return True, success_msg
        else:
            # 如果保存失败，删除已上传的照片
            if photo_filename:
                self.file_manager.delete_photo(team_name, photo_filename)
            return False, UIConstants.STATUS_MESSAGES["error_save_failed"]
    
    def delete_player(self, team_name: str, player_id: str) -> Tuple[bool, str]:
        """
        删除球员
        
        Args:
            team_name: 球队名称
            player_id: 球员ID
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        # 获取球员信息
        player = self.player_repo.get_player(team_name, player_id)
        if not player:
            return False, UIConstants.STATUS_MESSAGES["error_player_not_found"]
        
        # 删除照片文件
        if player.photo:
            self.file_manager.delete_photo(team_name, player.photo)
        
        # 删除球员数据
        if self.player_repo.delete_player(team_name, player_id):
            # 自动导出到AI处理文件夹
            self.export_service.auto_export_for_ai(team_name)

            # 标记球队状态已变化，并记录操作详情用于AI感知
            self._mark_team_stats_changed_with_action("delete_player", {
                "name": player.name,
                "jersey_number": player.jersey_number,
                "had_photo": player.photo is not None
            })

            success_msg = UIConstants.STATUS_MESSAGES["success_delete"].format(
                name=player.name
            )
            return True, success_msg
        else:
            return False, UIConstants.STATUS_MESSAGES["error_save_failed"]
    
    def get_players(self, team_name: str) -> List[Player]:
        """
        获取球队所有球员

        Args:
            team_name: 球队名称

        Returns:
            List[Player]: 球员列表
        """
        try:
            players = self.player_repo.get_players(team_name)
            # 验证球员数据的完整性
            validated_players = []
            for player in players:
                try:
                    # 检查球员照片路径是否有效
                    if hasattr(player, 'photo_path') and player.photo_path:
                        if not safe_file_manager.safe_file_exists(player.photo_path):
                            logger.warning(f"球员 {player.name} 的照片文件不存在: {player.photo_path}")
                            # 可以选择清空无效的照片路径
                            # player.photo_path = None
                    validated_players.append(player)
                except Exception as e:
                    logger.error(f"验证球员数据时出错: {e}")
                    # 仍然添加球员，但可能照片有问题
                    validated_players.append(player)

            return validated_players
        except Exception as e:
            logger.error(f"获取球员列表失败: {team_name}, 错误: {e}")
            safe_file_manager.handle_media_file_error(e, f"获取球员列表: {team_name}")
            return []
    
    def get_player(self, team_name: str, player_id: str) -> Optional[Player]:
        """
        获取球员信息
        
        Args:
            team_name: 球队名称
            player_id: 球员ID
            
        Returns:
            Optional[Player]: 球员对象
        """
        return self.player_repo.get_player(team_name, player_id)
    
    def get_used_jersey_numbers(self, team_name: str) -> List[str]:
        """
        获取已使用的球衣号码
        
        Args:
            team_name: 球队名称
            
        Returns:
            List[str]: 已使用的球衣号码列表
        """
        return self.player_repo.get_used_jersey_numbers(team_name)
    
    def get_players_with_photos(self, team_name: str) -> List[Player]:
        """
        获取有照片的球员
        
        Args:
            team_name: 球队名称
            
        Returns:
            List[Player]: 有照片的球员列表
        """
        return self.player_repo.get_players_with_photos(team_name)
    
    def get_players_without_photos(self, team_name: str) -> List[Player]:
        """
        获取没有照片的球员
        
        Args:
            team_name: 球队名称
            
        Returns:
            List[Player]: 没有照片的球员列表
        """
        return self.player_repo.get_players_without_photos(team_name)
    
    def update_player_photo(self, team_name: str, player_id: str, 
                           photo_file) -> Tuple[bool, str]:
        """
        更新球员照片
        
        Args:
            team_name: 球队名称
            player_id: 球员ID
            photo_file: 新照片文件
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        # 获取球员信息
        player = self.player_repo.get_player(team_name, player_id)
        if not player:
            return False, UIConstants.STATUS_MESSAGES["error_player_not_found"]
        
        # 验证照片文件
        is_valid, error_msg = DataValidator.validate_image_file(photo_file)
        if not is_valid:
            return False, error_msg
        
        # 删除旧照片
        if player.photo:
            self.file_manager.delete_photo(team_name, player.photo)
        
        # 保存新照片
        photo_filename = ImageProcessor.generate_unique_filename(photo_file.name)
        saved_path = self.file_manager.save_uploaded_photo(
            team_name, photo_file, photo_filename
        )
        
        if not saved_path:
            return False, "照片保存失败"
        
        # 更新球员信息
        player.set_photo(photo_filename)
        
        if self.player_repo.update_player(team_name, player):
            # 自动导出到AI处理文件夹
            self.export_service.auto_export_for_ai(team_name)

            # 标记球队状态已变化，并记录操作详情用于AI感知
            self._mark_team_stats_changed_with_action("update_photo", {
                "name": player.name,
                "jersey_number": player.jersey_number,
                "photo_updated": True
            })

            return True, "照片更新成功"
        else:
            # 如果更新失败，删除新上传的照片
            self.file_manager.delete_photo(team_name, photo_filename)
            return False, UIConstants.STATUS_MESSAGES["error_save_failed"]

    def _mark_team_stats_changed(self) -> None:
        """
        标记球队状态已变化，用于AI自动感知

        通过清除last_team_stats来强制AI在下次初始化时检测到变化
        """
        if "last_team_stats" in st.session_state:
            del st.session_state.last_team_stats

    def _mark_team_stats_changed_with_action(self, action_type: str, action_details: dict) -> None:
        """
        标记球队状态已变化，并记录具体操作详情用于AI感知

        Args:
            action_type: 操作类型 (add_player, delete_player, update_photo)
            action_details: 操作详情字典
        """
        # 清除缓存的统计信息
        if "last_team_stats" in st.session_state:
            del st.session_state.last_team_stats

        # 简单清理session state缓存
        current_team = st.session_state.get('current_team')
        if current_team:
            # 只清理session state中的相关缓存
            cache_keys_to_clear = [
                f'players_cache_{current_team}',
                f'team_stats_cache_{current_team}',
                'players_cache',
                'team_stats_cache',
                'player_list_cache'
            ]

            for key in cache_keys_to_clear:
                if key in st.session_state:
                    del st.session_state[key]

        # 记录最近的操作详情，供AI生成个性化消息
        st.session_state.last_player_action = {
            "type": action_type,
            "details": action_details,
            "timestamp": st.session_state.get("_timestamp", 0) + 1  # 简单的时间戳
        }
        st.session_state._timestamp = st.session_state.get("_timestamp", 0) + 1

    # 缓存清理方法已简化，移除复杂的缓存管理
