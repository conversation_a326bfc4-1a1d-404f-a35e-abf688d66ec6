#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用设置配置
Application Settings Configuration

集中管理应用程序的所有配置项
"""

import os
from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class PathSettings:
    """路径配置"""
    DATA_FOLDER: str = 'data'
    UPLOAD_FOLDER: str = 'uploads'
    AI_EXPORT_FOLDER: str = 'ai_export'
    PROCESSED_PHOTOS_FOLDER: str = 'processed_photos'
    WORD_OUTPUT_FOLDER: str = 'word_output'
    TEMP_FOLDER: str = 'temp'

    def ensure_directories(self) -> None:
        """确保所有必要的目录存在"""
        for folder in [self.DATA_FOLDER, self.UPLOAD_FOLDER,
                      self.AI_EXPORT_FOLDER, self.PROCESSED_PHOTOS_FOLDER,
                      self.WORD_OUTPUT_FOLDER, self.TEMP_FOLDER]:
            if not os.path.exists(folder):
                os.makedirs(folder)


@dataclass
class AISettings:
    """AI相关配置"""
    # OpenAI官方API配置（用于GPT对话、文本生成等）
    OPENAI_MODEL: str = "gpt-4o"
    OPENAI_BASE_URL: str = "https://api.openai.com/v1"

    # 302.ai API配置（专门用于图片生成）
    API_302_BASE_URL: str = "https://api.302.ai"
    API_302_KEY: str = os.getenv("API_302_KEY", "sk-Y5BDQdWlSCr2nQUYvbeKhEIwzzzg5AzGq8nTFR7KArlQuM3o")

    def get_openai_api_key(self) -> str:
        """获取OpenAI API Key，优先级：环境变量 > Streamlit secrets"""
        # 优先使用环境变量
        env_key = os.getenv("OPENAI_API_KEY")
        if env_key:
            return env_key

        # 回退到Streamlit secrets（需要在运行时获取）
        try:
            import streamlit as st
            if hasattr(st, 'secrets') and "OPENAI_API_KEY" in st.secrets:
                return st.secrets["OPENAI_API_KEY"]
        except:
            pass

        return ""

    SYSTEM_PROMPT_TEMPLATE: str = """你是一个专业的球队管理系统智能助手，了解以下系统功能和当前状态：

## 系统功能模块：
1. **球队管理**：创建、选择、管理球队
2. **球员管理**：添加、编辑、批量上传球员信息
3. **照片处理**：AI修图、批量处理球员照片
4. **报名表生成**：自动填写Word报名表
5. **数据导出**：导出球队数据供AI处理

## 当前球队状态：
- 球队名称：{team_name}
- 球员总数：{total_players}人
- 已上传照片：{players_with_photos}人
- 完成度：{completion_rate:.1f}%
- 状态：{team_status}

## 重要说明：
你正在为球队「{team_name}」收集基本信息。所有收集的数据都将自动关联到这个球队，无需用户重新输入球队名称。
如果用户提到其他球队名称，请友好地确认是否要将其设置为球队的显示名称，但数据仍然保存在当前球队「{team_name}」下。

## 你的主要任务：
帮助用户建立球队基本档案，只需收集最核心的3项信息：

### 用户必须提供的信息（仅3项）：
1. **联系人姓名**（通常是队长或负责人）
2. **联系电话**（11位手机号码）
3. **球衣主色调**（例如：红色、蓝色、白色等）

### 系统自动处理的信息：
- 球队名称：已知为「{team_name}」
- 领队姓名：自动填充为联系人
- 队医姓名：自动填充为联系人（可后续修改）
- 球裤颜色：AI根据球衣颜色智能搭配
- 球袜颜色：AI根据球衣颜色智能搭配
- 守门员服装：AI根据球衣颜色智能搭配

## 智能建议：
{smart_suggestions}

请用友好、简洁的语气与用户对话，一次只询问一个信息，确认收到后再询问下一个。
强调用户只需要提供3项基本信息，其他信息系统会智能处理。
记住：你正在为球队「{team_name}」收集信息，所有数据都会自动保存到这个球队下。"""


@dataclass
class WordGeneratorSettings:
    """Word生成器配置"""
    JAR_PATH: str = "../word_zc/ai-football-generator/target/word-generator.jar"
    TEMPLATE_PATH: str = "../word_zc/ai-football-generator/template.docx"
    OUTPUT_DIR: str = "word_output"
    TEMP_DIR: str = "temp"
    MAX_FILES: int = 20  # 最多保留的文件数量
    TIMEOUT: int = 60    # 生成超时时间（秒）

    def get_absolute_paths(self) -> Dict[str, str]:
        """获取绝对路径"""
        return {
            'jar_path': os.path.abspath(self.JAR_PATH),
            'template_path': os.path.abspath(self.TEMPLATE_PATH),
            'output_dir': os.path.abspath(self.OUTPUT_DIR),
            'temp_dir': os.path.abspath(self.TEMP_DIR)
        }


@dataclass
class AppSettings:
    """应用程序主要配置"""
    PAGE_TITLE: str = "淄川五人制球队管理系统"
    PAGE_ICON: str = "⚽"
    LAYOUT: str = "wide"
    INITIAL_SIDEBAR_STATE: str = "expanded"
    
    # 图片处理配置
    MAX_IMAGE_SIZE: tuple = (800, 800)
    IMAGE_QUALITY: int = 85
    
    # 文件上传配置
    MAX_FILE_SIZE_MB: int = 200
    
    # 分页配置
    PLAYERS_PER_ROW: int = 3
    BATCH_COLS_PER_ROW: int = 3
    
    def __post_init__(self):
        """初始化后处理"""
        self.paths = PathSettings()
        self.ai = AISettings()
        self.word_generator = WordGeneratorSettings()

        # 确保目录存在
        self.paths.ensure_directories()


# 全局配置实例
app_settings = AppSettings()
